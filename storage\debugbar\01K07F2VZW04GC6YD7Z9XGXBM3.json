{"__meta": {"id": "01K07F2VZW04GC6YD7Z9XGXBM3", "datetime": "2025-07-15 22:06:00", "utime": **********.640072, "method": "GET", "uri": "/cache/logo/bagisto.png", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752597358.676932, "end": **********.640101, "duration": 1.9631688594818115, "duration_str": "1.96s", "measures": [{"label": "Booting", "start": 1752597358.676932, "relative_start": 0, "end": **********.210386, "relative_end": **********.210386, "duration": 1.****************, "duration_str": "1.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.210413, "relative_start": 1.****************, "end": **********.640105, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "430ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.263525, "relative_start": 1.****************, "end": **********.277368, "relative_end": **********.277368, "duration": 0.013843059539794922, "duration_str": "13.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.626477, "relative_start": 1.***************, "end": **********.62697, "relative_end": **********.62697, "duration": 0.0004930496215820312, "duration_str": "493μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.627054, "relative_start": 1.****************, "end": **********.627135, "relative_end": **********.627135, "duration": 8.106231689453125e-05, "duration_str": "81μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.25", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/cache/logo/bagisto.png", "action_name": "image_cache", "controller_action": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage", "uri": "GET cache/{filename}", "controller": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Installer/src/Http/Controllers/ImageCacheController.php:30-41</a>", "duration": "1.97s", "peak_memory": "26MB", "response": "image/png", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1481679599 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1481679599\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1413104510 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1413104510\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-860327759 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">http://127.0.0.1:8001/admin/sales/performance/leaderboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6ImtxVlVsSEZpSEVDMWdsZ3NKYnR5eXc9PSIsInZhbHVlIjoiWEtJTUc4RGpYTXVEU0FPZ0UzRjVodDFwaDR4S0d1MjZCWEdXYTdFM0ltbGtPZjFGaFFRZmpLdlpSWHpUZitpbW9IZ2ZtTEdadVVqUG9XVk00R0RTWGk3dHU2NENQUlhSK0xHYW5wVmNIaTB1RWNFL29EbjE5RDNpRHVYWmU0Wk0iLCJtYWMiOiI3OGYxN2UzYzdmNzQ4MzBlNDcwMjM4N2ZkOTdiNTg0YmQ2NDNlOGY3NjFjZGNlZTlkMDJmZjVkZjk3Y2RjOTExIiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6ImQvSHFXZGZHRGhuOUlSVjRTcDRjRnc9PSIsInZhbHVlIjoib1lmRWd3K09kWTFMdDZmUmxDNFBtUnk0N0g3L0wrZVBKK0Q5NUtiSU9nRUlWdFlaaDJPU0E4Slo1ZG4yQ0ZKVFhwZFkrSXZBV0FYNnBjRGhCbjBRSWdNSjRTQ0tvc0Q5a0Z6Q0oyaXNleXNaOEUrcmdkakpROWlweFgxMWt4VW8iLCJtYWMiOiJhZTk5YmNkZDlkODhlYmFhN2E0YWM5YTYyZDgyMDc3YjY4MzNlZDk0MTQxOTIyYzA0NTBiODczZDkyN2RkYzAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860327759\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1796657428 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImtxVlVsSEZpSEVDMWdsZ3NKYnR5eXc9PSIsInZhbHVlIjoiWEtJTUc4RGpYTXVEU0FPZ0UzRjVodDFwaDR4S0d1MjZCWEdXYTdFM0ltbGtPZjFGaFFRZmpLdlpSWHpUZitpbW9IZ2ZtTEdadVVqUG9XVk00R0RTWGk3dHU2NENQUlhSK0xHYW5wVmNIaTB1RWNFL29EbjE5RDNpRHVYWmU0Wk0iLCJtYWMiOiI3OGYxN2UzYzdmNzQ4MzBlNDcwMjM4N2ZkOTdiNTg0YmQ2NDNlOGY3NjFjZGNlZTlkMDJmZjVkZjk3Y2RjOTExIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImQvSHFXZGZHRGhuOUlSVjRTcDRjRnc9PSIsInZhbHVlIjoib1lmRWd3K09kWTFMdDZmUmxDNFBtUnk0N0g3L0wrZVBKK0Q5NUtiSU9nRUlWdFlaaDJPU0E4Slo1ZG4yQ0ZKVFhwZFkrSXZBV0FYNnBjRGhCbjBRSWdNSjRTQ0tvc0Q5a0Z6Q0oyaXNleXNaOEUrcmdkakpROWlweFgxMWt4VW8iLCJtYWMiOiJhZTk5YmNkZDlkODhlYmFhN2E0YWM5YTYyZDgyMDc3YjY4MzNlZDk0MTQxOTIyYzA0NTBiODczZDkyN2RkYzAwIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796657428\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1400275538 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=10080, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">556</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">df1bdb0d95767d854463103bf5a03c96</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Jul 2025 16:36:00 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400275538\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-3770041 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-3770041\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/cache/logo/bagisto.png", "action_name": "image_cache", "controller_action": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage"}, "badge": null}}