{"__meta": {"id": "01K04VSNW2C8J1A4P99MB6S567", "datetime": "2025-07-14 21:50:27", "utime": **********.652062, "method": "GET", "uri": "/cache/logo/bagisto.png", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752510024.852694, "end": **********.65209, "duration": 2.79939603805542, "duration_str": "2.8s", "measures": [{"label": "Booting", "start": 1752510024.852694, "relative_start": 0, "end": **********.619625, "relative_end": **********.619625, "duration": 0.****************, "duration_str": "767ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.619651, "relative_start": 0.****************, "end": **********.652094, "relative_end": 3.814697265625e-06, "duration": 2.***************, "duration_str": "2.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.675384, "relative_start": 0.****************, "end": **********.695877, "relative_end": **********.695877, "duration": 0.020493030548095703, "duration_str": "20.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.647318, "relative_start": 2.***************, "end": **********.647688, "relative_end": **********.647688, "duration": 0.000370025634765625, "duration_str": "370μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.647785, "relative_start": 2.***************, "end": **********.647863, "relative_end": **********.647863, "duration": 7.796287536621094e-05, "duration_str": "78μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.25", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/cache/logo/bagisto.png", "action_name": "image_cache", "controller_action": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage", "uri": "GET cache/{filename}", "controller": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Installer/src/Http/Controllers/ImageCacheController.php:30-41</a>", "duration": "2.8s", "peak_memory": "24MB", "response": "image/png", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1674989468 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1674989468\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1989873262 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1989873262\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1633309547 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IlA2dHFkZm03dmhTVlZRN3lEenVIZ3c9PSIsInZhbHVlIjoiWndLK2VkSHZhbThjb3kydjdObTE3VkhrRVhuUHRFamdRc2RqdHRSaFFYOWdMdEdBTUtTclh0SkxuUnErR1dRZThzWmpBeERGK1hiTTh4cWFEZ2NIWTJRWGVlalFEdjdwak05TFVlSlBYMmVKR09iaGFTRit4TnBxWWdjcWZXY2MiLCJtYWMiOiI5MzliMzU2YzNjOGYzOGQ5M2M0MTc4N2UyYjA1MjA3ZDk5MDQ3NzIwYTBiNmIzOWIzOTNiZjAwOGM4NDJkNjBlIiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6IklqODhzSWRlbjZQSzhRMWxjZzdXM0E9PSIsInZhbHVlIjoiT284QTIwaUxSOEtydk16K2tBYWwxV1d5RFZUUWNwZlBTVnhWODF1L3JlT3dySUxWVXlEb0Y1N2hnbWlnZk1aYzR2OW9QLzFzRk9YcUc2bzlpNmV1UkZJdlpmUU5KanJHdTE2ejNDM0F2dUFJNUM0YjQ4UXk4dFdoUVBmeTJ4cFMiLCJtYWMiOiI1ZWZlMWNkZDFmNGM3NTZkZDAyOGM3MWMwMTQyMjQzMDNjYzY4NjVhNWI2YjcwNjBlZGU4NzM2NjgwNTA2ODdhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1633309547\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-713333089 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlA2dHFkZm03dmhTVlZRN3lEenVIZ3c9PSIsInZhbHVlIjoiWndLK2VkSHZhbThjb3kydjdObTE3VkhrRVhuUHRFamdRc2RqdHRSaFFYOWdMdEdBTUtTclh0SkxuUnErR1dRZThzWmpBeERGK1hiTTh4cWFEZ2NIWTJRWGVlalFEdjdwak05TFVlSlBYMmVKR09iaGFTRit4TnBxWWdjcWZXY2MiLCJtYWMiOiI5MzliMzU2YzNjOGYzOGQ5M2M0MTc4N2UyYjA1MjA3ZDk5MDQ3NzIwYTBiNmIzOWIzOTNiZjAwOGM4NDJkNjBlIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IklqODhzSWRlbjZQSzhRMWxjZzdXM0E9PSIsInZhbHVlIjoiT284QTIwaUxSOEtydk16K2tBYWwxV1d5RFZUUWNwZlBTVnhWODF1L3JlT3dySUxWVXlEb0Y1N2hnbWlnZk1aYzR2OW9QLzFzRk9YcUc2bzlpNmV1UkZJdlpmUU5KanJHdTE2ejNDM0F2dUFJNUM0YjQ4UXk4dFdoUVBmeTJ4cFMiLCJtYWMiOiI1ZWZlMWNkZDFmNGM3NTZkZDAyOGM3MWMwMTQyMjQzMDNjYzY4NjVhNWI2YjcwNjBlZGU4NzM2NjgwNTA2ODdhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-713333089\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1569423766 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=10080, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">556</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">df1bdb0d95767d854463103bf5a03c96</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 16:20:27 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569423766\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1567193400 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1567193400\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/cache/logo/bagisto.png", "action_name": "image_cache", "controller_action": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage"}, "badge": null}}