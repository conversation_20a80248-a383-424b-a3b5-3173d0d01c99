<?php

namespace Webkul\Sales\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Webkul\User\Models\User;

use Webkul\Sales\Contracts\SalesTarget as SalesTargetContract;

class SalesTarget extends Model implements SalesTargetContract
{
    protected $fillable = [
        'name',
        'description',
        'target_amount',
        'achieved_amount',
        'assignee_type',
        'assignee_id',
        'assignee_name',
        'start_date',
        'end_date',
        'period_type',
        'status',
        'progress_percentage',
        'last_calculated_at',
        'notes',
        'attachments',
        'metadata',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'target_amount'        => 'decimal:2',
        'achieved_amount'      => 'decimal:2',
        'progress_percentage'  => 'decimal:2',
        'start_date'          => 'date',
        'end_date'            => 'date',
        'last_calculated_at'  => 'datetime',
        'attachments'         => 'array',
        'metadata'            => 'array',
    ];

    /**
     * Get the user who created this target.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this target.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the adjustments for this target.
     */
    public function adjustments(): HasMany
    {
        return $this->hasMany(SalesTargetAdjustment::class);
    }

    /**
     * Get the assignee based on assignee_type.
     */
    public function assignee()
    {
        switch ($this->assignee_type) {
            case 'individual':
                return $this->belongsTo(User::class, 'assignee_id');
            case 'team':
                return $this->belongsTo(SalesTeam::class, 'assignee_id');
            case 'region':
                return $this->belongsTo(SalesRegion::class, 'assignee_id');
            default:
                return null;
        }
    }

    /**
     * Calculate and update progress percentage.
     */
    public function updateProgress(): void
    {
        if ($this->target_amount > 0) {
            $this->progress_percentage = min(100, ($this->achieved_amount / $this->target_amount) * 100);
        } else {
            $this->progress_percentage = 0;
        }

        $this->last_calculated_at = now();
        $this->save();
    }

    /**
     * Check if target is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && 
               $this->start_date <= now()->toDateString() && 
               $this->end_date >= now()->toDateString();
    }

    /**
     * Check if target is achieved.
     */
    public function isAchieved(): bool
    {
        return $this->progress_percentage >= 100;
    }

    /**
     * Scope for active targets.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('start_date', '<=', now()->toDateString())
                    ->where('end_date', '>=', now()->toDateString());
    }

    /**
     * Scope for targets by assignee.
     */
    public function scopeForAssignee($query, string $type, int $id)
    {
        return $query->where('assignee_type', $type)
                    ->where('assignee_id', $id);
    }
}
