{"__meta": {"id": "01K07F2YYEE3YVJRBSV5QVSY8N", "datetime": "2025-07-15 22:06:03", "utime": **********.667294, "method": "GET", "uri": "/cache/logo.png", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752597362.720799, "end": **********.66732, "duration": 0.9465210437774658, "duration_str": "947ms", "measures": [{"label": "Booting", "start": 1752597362.720799, "relative_start": 0, "end": **********.566176, "relative_end": **********.566176, "duration": 0.****************, "duration_str": "845ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.566269, "relative_start": 0.****************, "end": **********.66739, "relative_end": 7.009506225585938e-05, "duration": 0.*****************, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.6175, "relative_start": 0.****************, "end": **********.63787, "relative_end": **********.63787, "duration": 0.020370006561279297, "duration_str": "20.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.654788, "relative_start": 0.****************, "end": **********.655118, "relative_end": **********.655118, "duration": 0.0003299713134765625, "duration_str": "330μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.655196, "relative_start": 0.****************, "end": **********.655266, "relative_end": **********.655266, "duration": 7.009506225585938e-05, "duration_str": "70μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.25", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/cache/logo.png", "action_name": "image_cache", "controller_action": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage", "uri": "GET cache/{filename}", "controller": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Installer/src/Http/Controllers/ImageCacheController.php:30-41</a>", "duration": "949ms", "peak_memory": "26MB", "response": "image/png", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1683480658 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1683480658\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-193788168 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-193788168\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1262391869 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">http://127.0.0.1:8001/admin/sales/performance/leaderboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6Ijl0VzNhcDVzN0pJOC9aYzdyZVlIb3c9PSIsInZhbHVlIjoiNXVOaVRBdnlQck1NRzB0LzYrdmx1eGlJT2RxZGY5T3dnWXRjRXdqMUlDMXlneXVsQmZuaXJzbkpMeWhhMGhaeXZjNlBlWUFYcjVvSkhadjJPL3h1c2l0TkFqYXhxRFFBTDV2Q29BWnNyWVNsRkNlYnNIT3VNamxVVnV0YndTOGgiLCJtYWMiOiI5N2FiYTJkOTdkNzVhMzcxM2NiNjcxNzI1ZjczNWQxNjk2YmQ3YjFiNmFmMWU0Mjk1Nzc0Y2UxZTMwOTMzYjI1IiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6InZSRlovRk00d3VtbTdMZ3BJMTYvQmc9PSIsInZhbHVlIjoibG8xM29tZS9TRmFwR3NlNWxiZlNVdWg1eUF1Q0tZbVlDSU1reGc5WTFVR2h3T2wrQ2ZvMGN0Z2ZUMnBjTEMySUxGSGU0cy9xOGtpaXl0SXFsKzJYdmtGYUUrRHQ5bWNIVkkvUHFQdU1lZmRTZHl5ZE9wUzdmQytZOWV4NFk0WlEiLCJtYWMiOiI0OWQyNWE5MTViOWJlNGVlY2JlZGY3N2RjMGFjN2Q3NTYyNzA1Yjg1M2IyZTg3OTg4OTZlNzNjNjMxYmI0ZmIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1262391869\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1878327101 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ijl0VzNhcDVzN0pJOC9aYzdyZVlIb3c9PSIsInZhbHVlIjoiNXVOaVRBdnlQck1NRzB0LzYrdmx1eGlJT2RxZGY5T3dnWXRjRXdqMUlDMXlneXVsQmZuaXJzbkpMeWhhMGhaeXZjNlBlWUFYcjVvSkhadjJPL3h1c2l0TkFqYXhxRFFBTDV2Q29BWnNyWVNsRkNlYnNIT3VNamxVVnV0YndTOGgiLCJtYWMiOiI5N2FiYTJkOTdkNzVhMzcxM2NiNjcxNzI1ZjczNWQxNjk2YmQ3YjFiNmFmMWU0Mjk1Nzc0Y2UxZTMwOTMzYjI1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InZSRlovRk00d3VtbTdMZ3BJMTYvQmc9PSIsInZhbHVlIjoibG8xM29tZS9TRmFwR3NlNWxiZlNVdWg1eUF1Q0tZbVlDSU1reGc5WTFVR2h3T2wrQ2ZvMGN0Z2ZUMnBjTEMySUxGSGU0cy9xOGtpaXl0SXFsKzJYdmtGYUUrRHQ5bWNIVkkvUHFQdU1lZmRTZHl5ZE9wUzdmQytZOWV4NFk0WlEiLCJtYWMiOiI0OWQyNWE5MTViOWJlNGVlY2JlZGY3N2RjMGFjN2Q3NTYyNzA1Yjg1M2IyZTg3OTg4OTZlNzNjNjMxYmI0ZmIxIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1878327101\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1839965879 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=10080, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">556</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">df1bdb0d95767d854463103bf5a03c96</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Jul 2025 16:36:03 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1839965879\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-161843548 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-161843548\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/cache/logo.png", "action_name": "image_cache", "controller_action": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage"}, "badge": null}}