{"__meta": {"id": "01K04VTPJ3R2EPGFENZB9139D0", "datetime": "2025-07-14 21:51:01", "utime": **********.127613, "method": "GET", "uri": "/cache/logo.png", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752510060.543528, "end": **********.127641, "duration": 0.5841128826141357, "duration_str": "584ms", "measures": [{"label": "Booting", "start": 1752510060.543528, "relative_start": 0, "end": **********.026523, "relative_end": **********.026523, "duration": 0.*****************, "duration_str": "483ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.026542, "relative_start": 0.****************, "end": **********.127644, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.059641, "relative_start": 0.****************, "end": **********.06937, "relative_end": **********.06937, "duration": 0.009729146957397461, "duration_str": "9.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.120242, "relative_start": 0.***************, "end": **********.120637, "relative_end": **********.120637, "duration": 0.0003948211669921875, "duration_str": "395μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.120736, "relative_start": 0.****************, "end": **********.120842, "relative_end": **********.120842, "duration": 0.00010609626770019531, "duration_str": "106μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.25", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/cache/logo.png", "action_name": "image_cache", "controller_action": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage", "uri": "GET cache/{filename}", "controller": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Installer/src/Http/Controllers/ImageCacheController.php:30-41</a>", "duration": "588ms", "peak_memory": "24MB", "response": "image/png", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1978235329 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1978235329\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1450088666 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1450088666\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-387535026 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6InF5T1lOTW1EQ2pwNDI4NTVWdmdBSHc9PSIsInZhbHVlIjoickErdFZWNkZmTXdUYmFSZUNCL3J3OVoxTGk4YjJhMit2QkxzR0toTzl4VmZkRFM4VWJGc1VzK1E3ckE3NUkrVWtyU21UYm52TjVxc2NLcUxHR0dqT2JrbXZxeEtQRHVYbHA3eENuMEp0Y0xsRVVLTnhLeTFxZFVMcjNZMHV2RysiLCJtYWMiOiIyMzkzY2U4ZmU4ODM2ODEyOWQ5NWNhMjhhYzMyZWNkZWQxZWEyNDY4YjI2NmM5MzdmMjY2M2Q5MDQ2NTRlZjhlIiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6Ii8vNjNQd3JJVmFkMGFLYlBkY0hwQkE9PSIsInZhbHVlIjoiTXBqL0R3V0N1dkQ3Tk9KZGRxby9qNzNoOUR1dWd5Q1hTMFhhMGhoRFVGQzEvQXJZR25DY2xZNElhaE1UNUlGeFpaVy94UFM1VUlDVENna215QTZIbWZ5TGJPbDBEOFdjbHZ3a1ZzZEpxK2Q2YmR3NzJ1YUpTdnE2VG1UcFZPcUUiLCJtYWMiOiI5NjM4ZmI3NjQ4ZjQyZTE0OGYxYjBjYTdjN2IwMDIzOTUyZDU4NWQzNWQ4MjA0YWYxODAwZWY3NDA2YWZjMDVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387535026\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-391448117 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InF5T1lOTW1EQ2pwNDI4NTVWdmdBSHc9PSIsInZhbHVlIjoickErdFZWNkZmTXdUYmFSZUNCL3J3OVoxTGk4YjJhMit2QkxzR0toTzl4VmZkRFM4VWJGc1VzK1E3ckE3NUkrVWtyU21UYm52TjVxc2NLcUxHR0dqT2JrbXZxeEtQRHVYbHA3eENuMEp0Y0xsRVVLTnhLeTFxZFVMcjNZMHV2RysiLCJtYWMiOiIyMzkzY2U4ZmU4ODM2ODEyOWQ5NWNhMjhhYzMyZWNkZWQxZWEyNDY4YjI2NmM5MzdmMjY2M2Q5MDQ2NTRlZjhlIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ii8vNjNQd3JJVmFkMGFLYlBkY0hwQkE9PSIsInZhbHVlIjoiTXBqL0R3V0N1dkQ3Tk9KZGRxby9qNzNoOUR1dWd5Q1hTMFhhMGhoRFVGQzEvQXJZR25DY2xZNElhaE1UNUlGeFpaVy94UFM1VUlDVENna215QTZIbWZ5TGJPbDBEOFdjbHZ3a1ZzZEpxK2Q2YmR3NzJ1YUpTdnE2VG1UcFZPcUUiLCJtYWMiOiI5NjM4ZmI3NjQ4ZjQyZTE0OGYxYjBjYTdjN2IwMDIzOTUyZDU4NWQzNWQ4MjA0YWYxODAwZWY3NDA2YWZjMDVjIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391448117\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1596135347 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=10080, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">556</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">df1bdb0d95767d854463103bf5a03c96</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 16:21:01 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1596135347\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1501470210 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1501470210\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/cache/logo.png", "action_name": "image_cache", "controller_action": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage"}, "badge": null}}