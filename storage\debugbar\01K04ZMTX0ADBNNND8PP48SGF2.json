{"__meta": {"id": "01K04ZMTX0ADBNNND8PP48SGF2", "datetime": "2025-07-14 22:57:43", "utime": **********.267948, "method": "GET", "uri": "/cache/logo/bagisto.png", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[22:57:43] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Installer\\src\\Http\\Controllers\\ImageCacheController.php on line 104", "message_html": null, "is_string": false, "label": "warning", "time": **********.254593, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752514060.650154, "end": **********.267984, "duration": 2.6178297996520996, "duration_str": "2.62s", "measures": [{"label": "Booting", "start": 1752514060.650154, "relative_start": 0, "end": **********.519079, "relative_end": **********.519079, "duration": 0.****************, "duration_str": "869ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.519106, "relative_start": 0.****************, "end": **********.267988, "relative_end": 4.0531158447265625e-06, "duration": 1.****************, "duration_str": "1.75s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.553888, "relative_start": 0.****************, "end": **********.567535, "relative_end": **********.567535, "duration": 0.013646841049194336, "duration_str": "13.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.258197, "relative_start": 2.****************, "end": **********.258919, "relative_end": **********.258919, "duration": 0.0007219314575195312, "duration_str": "722μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.259042, "relative_start": 2.****************, "end": **********.259135, "relative_end": **********.259135, "duration": 9.298324584960938e-05, "duration_str": "93μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.25", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "304 Not Modified", "full_url": "http://127.0.0.1:8000/cache/logo/bagisto.png", "action_name": "image_cache", "controller_action": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage", "uri": "GET cache/{filename}", "controller": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Installer/src/Http/Controllers/ImageCacheController.php:30-41</a>", "duration": "2.62s", "peak_memory": "26MB", "response": "text/html", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-162649689 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-162649689\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1892527904 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1892527904\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1948660655 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-US,en;q=0.9,hi;q=0.8,en-IN;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6ImlZN2RrZzIvYUNsSjJwbTVwdzBkb2c9PSIsInZhbHVlIjoieWhyRnhoczBNbkIzclpkcnNrWDF1cUU4K096bWtQNE00OCs0ak42SWFuZEczV1l6bFlscnJaWUZ4TlNOUWc5RkZqMmk2eWRhekQvaU1sQ2QrR3BMU1ZjUXZxRXJqdHd3RDY3bndlQlZIcnNTTFMwVTlJQU5wa2twZFJwdG8vMGIiLCJtYWMiOiIyNmRiZWJkNzk1MzA4ZjY1OTMxMmVmZTRmZThkNjMzNWM2MDcyNzZmNThhNDFjZTdlMzEzZjIzM2IzMWE0MDNlIiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6InFuM1NDQ2o5WkZNb2NhTjJSSjBPSXc9PSIsInZhbHVlIjoiV0RsL2JiZGVjVmJFWVlaQW5jVFFjT3VFem9nS0V4MkQrNTNVNnJvcmZzYVNMS0o3OUg3eWxNVm1KaVlmazlDODdwanhYVm1DZVpGSlFOSmRmU3p5N0xFKzBlZU9LZzVaRG94SThuc0QybkQwL2sxY2NXMU8yb2xMdi9sTUtyWEkiLCJtYWMiOiIxZTJhOTA5ODdmMWY4ZTA0MWU1NGRhMDQyMzlmZmY5YTc2ZDM5NmMyMDdkOTNiMzIwYTY5MTRhODEzOTViNzMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>if-none-match</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">df1bdb0d95767d854463103bf5a03c96</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948660655\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1677009969 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImlZN2RrZzIvYUNsSjJwbTVwdzBkb2c9PSIsInZhbHVlIjoieWhyRnhoczBNbkIzclpkcnNrWDF1cUU4K096bWtQNE00OCs0ak42SWFuZEczV1l6bFlscnJaWUZ4TlNOUWc5RkZqMmk2eWRhekQvaU1sQ2QrR3BMU1ZjUXZxRXJqdHd3RDY3bndlQlZIcnNTTFMwVTlJQU5wa2twZFJwdG8vMGIiLCJtYWMiOiIyNmRiZWJkNzk1MzA4ZjY1OTMxMmVmZTRmZThkNjMzNWM2MDcyNzZmNThhNDFjZTdlMzEzZjIzM2IzMWE0MDNlIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InFuM1NDQ2o5WkZNb2NhTjJSSjBPSXc9PSIsInZhbHVlIjoiV0RsL2JiZGVjVmJFWVlaQW5jVFFjT3VFem9nS0V4MkQrNTNVNnJvcmZzYVNMS0o3OUg3eWxNVm1KaVlmazlDODdwanhYVm1DZVpGSlFOSmRmU3p5N0xFKzBlZU9LZzVaRG94SThuc0QybkQwL2sxY2NXMU8yb2xMdi9sTUtyWEkiLCJtYWMiOiIxZTJhOTA5ODdmMWY4ZTA0MWU1NGRhMDQyMzlmZmY5YTc2ZDM5NmMyMDdkOTNiMzIwYTY5MTRhODEzOTViNzMyIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677009969\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-229339317 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=10080, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">df1bdb0d95767d854463103bf5a03c96</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:27:43 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-229339317\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1650513489 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UkIlDY4pOLJdsmToM4ZN03LZ2SvxDtusFOt9HuKM</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650513489\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "304 Not Modified", "full_url": "http://127.0.0.1:8000/cache/logo/bagisto.png", "action_name": "image_cache", "controller_action": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage"}, "badge": "304 Not Modified"}}