<?php

/**
 * Test script to verify Sales module routes are working
 */

require_once 'vendor/autoload.php';

$routes = [
    'admin/sales/dashboard',
    'admin/sales/targets',
    'admin/sales/performance',
    'admin/sales/reports',
    'admin/sales/targets/create',
    'admin/sales/reports/create',
];

$baseUrl = 'http://127.0.0.1:8000/';

echo "Testing Sales Module Routes:\n";
echo "============================\n\n";

foreach ($routes as $route) {
    $url = $baseUrl . $route;
    echo "Testing: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode == 200) {
        echo "✅ SUCCESS - HTTP $httpCode\n";
    } elseif ($httpCode == 302) {
        echo "🔄 REDIRECT - HTTP $httpCode (likely authentication redirect)\n";
    } else {
        echo "❌ FAILED - HTTP $httpCode\n";
    }
    echo "\n";
}

echo "Test completed!\n";
