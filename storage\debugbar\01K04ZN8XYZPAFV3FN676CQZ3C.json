{"__meta": {"id": "01K04ZN8XYZPAFV3FN676CQZ3C", "datetime": "2025-07-14 22:57:57", "utime": **********.634765, "method": "GET", "uri": "/cache/logo.png", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[22:57:57] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Installer\\src\\Http\\Controllers\\ImageCacheController.php on line 104", "message_html": null, "is_string": false, "label": "warning", "time": **********.626153, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752514076.840285, "end": **********.634793, "duration": 0.7945079803466797, "duration_str": "795ms", "measures": [{"label": "Booting", "start": 1752514076.840285, "relative_start": 0, "end": **********.487111, "relative_end": **********.487111, "duration": 0.****************, "duration_str": "647ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.487137, "relative_start": 0.****************, "end": **********.634795, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.54647, "relative_start": 0.****************, "end": **********.554958, "relative_end": **********.554958, "duration": 0.008488178253173828, "duration_str": "8.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.626824, "relative_start": 0.**************, "end": **********.627252, "relative_end": **********.627252, "duration": 0.00042819976806640625, "duration_str": "428μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.627369, "relative_start": 0.****************, "end": **********.62748, "relative_end": **********.62748, "duration": 0.00011110305786132812, "duration_str": "111μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.25", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "304 Not Modified", "full_url": "http://127.0.0.1:8000/cache/logo.png", "action_name": "image_cache", "controller_action": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage", "uri": "GET cache/{filename}", "controller": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Installer/src/Http/Controllers/ImageCacheController.php:30-41</a>", "duration": "796ms", "peak_memory": "26MB", "response": "text/html", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1726082879 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1726082879\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1600357918 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1600357918\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1483893007 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-US,en;q=0.9,hi;q=0.8,en-IN;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6Ik5ySmVkcHBLL1RpbktEaStnbUw3K1E9PSIsInZhbHVlIjoidEp0RXhEalBkSXQwYi9JangvSHFJRFFhaFFFMmNFMFcxOVVBejE2SjA2dElUU205cVhnWDh3SGIwallrdGE5TS9GR2RBY0tJOHpROCtUY2NpN05tcEFObzRzQmlYdGJtSHhlbGhiUVJnZ0RHQnpac3hGUmJVR2U1NzRPZnNoVC8iLCJtYWMiOiI3YmQyYTA0NWFlZTYyZThiZDI4MTcxNTE1MTI1MDc4OGEzODI5Njk2MWMwOWYxYTQ0Y2QzNzcyODRmMjM3ODMzIiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6Ik1MUHlsUTlqRWFWSi9oTnpOYWl6M2c9PSIsInZhbHVlIjoiUFplMGV1aDNDak1qY0NKY1pCd0N1NkFrcTlCaTlFUlNLODcwZVNJUVVTYVVEc0xuOVFHbDhnVnNKOGFHMmx1YklFQXRxd2l4dVdZb01HbW5SeTRhNTdFdHBQRDJVVC8wSVI3azZ3dmlGVlNBbktwa3BjL1YzNFVkU2dYczlUaloiLCJtYWMiOiI2MDkyMThkMGIzYTZiNWUyMjdiZDU0M2RhZDQ2YjZlNzFhZDE2OWE3OWU0ZGMzMjMzOWY1ODEyNzllOTFjMTc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>if-none-match</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">df1bdb0d95767d854463103bf5a03c96</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1483893007\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1082405265 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik5ySmVkcHBLL1RpbktEaStnbUw3K1E9PSIsInZhbHVlIjoidEp0RXhEalBkSXQwYi9JangvSHFJRFFhaFFFMmNFMFcxOVVBejE2SjA2dElUU205cVhnWDh3SGIwallrdGE5TS9GR2RBY0tJOHpROCtUY2NpN05tcEFObzRzQmlYdGJtSHhlbGhiUVJnZ0RHQnpac3hGUmJVR2U1NzRPZnNoVC8iLCJtYWMiOiI3YmQyYTA0NWFlZTYyZThiZDI4MTcxNTE1MTI1MDc4OGEzODI5Njk2MWMwOWYxYTQ0Y2QzNzcyODRmMjM3ODMzIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik1MUHlsUTlqRWFWSi9oTnpOYWl6M2c9PSIsInZhbHVlIjoiUFplMGV1aDNDak1qY0NKY1pCd0N1NkFrcTlCaTlFUlNLODcwZVNJUVVTYVVEc0xuOVFHbDhnVnNKOGFHMmx1YklFQXRxd2l4dVdZb01HbW5SeTRhNTdFdHBQRDJVVC8wSVI3azZ3dmlGVlNBbktwa3BjL1YzNFVkU2dYczlUaloiLCJtYWMiOiI2MDkyMThkMGIzYTZiNWUyMjdiZDU0M2RhZDQ2YjZlNzFhZDE2OWE3OWU0ZGMzMjMzOWY1ODEyNzllOTFjMTc0IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082405265\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-634849338 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=10080, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">df1bdb0d95767d854463103bf5a03c96</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 17:27:57 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-634849338\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-854078363 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q747s1PaQJyjKB4XBbv8NGmTsUD2qzGC9FU87NXD</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-854078363\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "304 Not Modified", "full_url": "http://127.0.0.1:8000/cache/logo.png", "action_name": "image_cache", "controller_action": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage"}, "badge": "304 Not Modified"}}