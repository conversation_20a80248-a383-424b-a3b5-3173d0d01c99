{"__meta": {"id": "01K07BY6NYDTHH5H07ZCJ2CZ00", "datetime": "2025-07-15 21:11:02", "utime": **********.017132, "method": "GET", "uri": "/cache/logo/bagisto.png", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[21:11:02] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages\\Webkul\\Installer\\src\\Http\\Controllers\\ImageCacheController.php on line 104", "message_html": null, "is_string": false, "label": "warning", "time": **********.01099, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.38949, "end": **********.017149, "duration": 1.6276590824127197, "duration_str": "1.63s", "measures": [{"label": "Booting", "start": **********.38949, "relative_start": 0, "end": **********.649664, "relative_end": **********.649664, "duration": 0.***************, "duration_str": "260ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.64968, "relative_start": 0.****************, "end": **********.01715, "relative_end": 9.5367431640625e-07, "duration": 1.****************, "duration_str": "1.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.668143, "relative_start": 0.****************, "end": **********.673913, "relative_end": **********.673913, "duration": 0.005769968032836914, "duration_str": "5.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.011824, "relative_start": 1.****************, "end": **********.01272, "relative_end": **********.01272, "duration": 0.0008962154388427734, "duration_str": "896μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.012788, "relative_start": 1.***************, "end": **********.012858, "relative_end": **********.012858, "duration": 6.985664367675781e-05, "duration_str": "70μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.25", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "304 Not Modified", "full_url": "http://127.0.0.1:8000/cache/logo/bagisto.png", "action_name": "image_cache", "controller_action": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage", "uri": "GET cache/{filename}", "controller": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm2%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Installer/src/Http/Controllers/ImageCacheController.php:30-41</a>", "duration": "1.63s", "peak_memory": "26MB", "response": "text/html", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2078161055 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2078161055\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1964179468 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1964179468\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-251328010 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/admin/sales/reports</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IkRhd3Z4RTJMY2Z3aStaYjlaeXZaQ3c9PSIsInZhbHVlIjoiNENrZ0kwOEdBbkpiVENVUVZxNWZkU2lwSmpwdFFKRmpha2ZoYVR1c3J5eTFpb1d1c1IxVG5WS0ZLZXp6SkVaYWI2MEdXZzVXZXA3d1J4aEZEMXEwb2xLZ09RS3BaeHFoY0ZhM05pT3dGakc0YktXWlhPYzFvY01qWlpwQWtqMzYiLCJtYWMiOiIzOTkxYzkyOTIxN2MzMjg2N2I4YTQ0MGJiOGI1MTVjOTJkZGFjOTVkZGRkZDZkOTFlNTRiZmE1ZjQzODc4Yzc4IiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6IkVaM0ptaDc3SjZTVFRpQlc4MVNSTGc9PSIsInZhbHVlIjoiVU1QSXdhN2IrMmhrWm1DSSttekZ5ZTNjaEJqaEgwSllFekY0YlFSSmsvV2FSVnpvRVFPTGVuNzNZRDM1U2Z3Q3Y3VDR5NlFJQVNDSjBKb2xkaWZCRFNCUWsxcWFaakc0SmNRMWk5Zk5SSkRYQ0hNUENwckdaVEx2SGdDQWNzUk4iLCJtYWMiOiI0NGQ2YmZlZDU5ODViNmMzMGU2YTg3MjRmNTk5ZDFlNGRkZmMyMzE4YWE4YzgxZTQ2ZTNkMDU2NWMwZWQxNjc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>if-none-match</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">df1bdb0d95767d854463103bf5a03c96</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251328010\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1077675749 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkRhd3Z4RTJMY2Z3aStaYjlaeXZaQ3c9PSIsInZhbHVlIjoiNENrZ0kwOEdBbkpiVENVUVZxNWZkU2lwSmpwdFFKRmpha2ZoYVR1c3J5eTFpb1d1c1IxVG5WS0ZLZXp6SkVaYWI2MEdXZzVXZXA3d1J4aEZEMXEwb2xLZ09RS3BaeHFoY0ZhM05pT3dGakc0YktXWlhPYzFvY01qWlpwQWtqMzYiLCJtYWMiOiIzOTkxYzkyOTIxN2MzMjg2N2I4YTQ0MGJiOGI1MTVjOTJkZGFjOTVkZGRkZDZkOTFlNTRiZmE1ZjQzODc4Yzc4IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkVaM0ptaDc3SjZTVFRpQlc4MVNSTGc9PSIsInZhbHVlIjoiVU1QSXdhN2IrMmhrWm1DSSttekZ5ZTNjaEJqaEgwSllFekY0YlFSSmsvV2FSVnpvRVFPTGVuNzNZRDM1U2Z3Q3Y3VDR5NlFJQVNDSjBKb2xkaWZCRFNCUWsxcWFaakc0SmNRMWk5Zk5SSkRYQ0hNUENwckdaVEx2SGdDQWNzUk4iLCJtYWMiOiI0NGQ2YmZlZDU5ODViNmMzMGU2YTg3MjRmNTk5ZDFlNGRkZmMyMzE4YWE4YzgxZTQ2ZTNkMDU2NWMwZWQxNjc4IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077675749\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1699532860 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=10080, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">df1bdb0d95767d854463103bf5a03c96</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Jul 2025 15:41:02 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1699532860\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-311386810 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">09cowQ19jIT1y8ogZLl9ONKUh4p0uxC0tI9IIYeV</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311386810\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "304 Not Modified", "full_url": "http://127.0.0.1:8000/cache/logo/bagisto.png", "action_name": "image_cache", "controller_action": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage"}, "badge": "304 Not Modified"}}