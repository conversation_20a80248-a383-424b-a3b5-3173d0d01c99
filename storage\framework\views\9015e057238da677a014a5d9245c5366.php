<?php $__env->startSection('title'); ?>
    <?php echo e(trans('sales::app.reports.title')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="flex flex-col gap-4">
        <?php echo view_render_event('sales.reports.index.header.before'); ?>


        <!-- Page Header -->
        <div class="flex flex-col gap-2">
            <div class="flex items-center justify-between">
                <div class="flex flex-col gap-1">
                    <p class="text-xl font-bold leading-6 text-gray-800 dark:text-white">
                        <?php echo e(trans('sales::app.reports.title')); ?>

                    </p>

                    <p class="text-sm text-gray-600 dark:text-gray-300">
                        <?php echo e(trans('sales::app.reports.description')); ?>

                    </p>
                </div>

                <div class="flex items-center gap-x-2.5">
                    <!-- Create Report Button -->
                    <div class="flex items-center gap-x-2.5">
                        <a
                            href="<?php echo e(route('admin.sales.reports.create')); ?>"
                            class="primary-button"
                        >
                            <?php echo e(trans('sales::app.reports.create-btn-title')); ?>

                        </a>
                    </div>
                </div>
            </div>
        </div>

        <?php echo view_render_event('sales.reports.index.header.after'); ?>


        <?php echo view_render_event('sales.reports.index.datagrid.before'); ?>


        <!-- DataGrid -->
        <?php if (isset($component)) { $__componentOriginal3bea17ac3f7235e71a823454ccb74424 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3bea17ac3f7235e71a823454ccb74424 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.datagrid.index','data' => ['src' => route('admin.sales.reports.index'),'ref' => 'datagrid']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::datagrid'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['src' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.sales.reports.index')),'ref' => 'datagrid']); ?>
            <!-- DataGrid Shimmer -->
            <?php if (isset($component)) { $__componentOriginal6de075cdae15a153e978193a85b13d2e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6de075cdae15a153e978193a85b13d2e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.datagrid.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.datagrid'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6de075cdae15a153e978193a85b13d2e)): ?>
<?php $attributes = $__attributesOriginal6de075cdae15a153e978193a85b13d2e; ?>
<?php unset($__attributesOriginal6de075cdae15a153e978193a85b13d2e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6de075cdae15a153e978193a85b13d2e)): ?>
<?php $component = $__componentOriginal6de075cdae15a153e978193a85b13d2e; ?>
<?php unset($__componentOriginal6de075cdae15a153e978193a85b13d2e); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3bea17ac3f7235e71a823454ccb74424)): ?>
<?php $attributes = $__attributesOriginal3bea17ac3f7235e71a823454ccb74424; ?>
<?php unset($__attributesOriginal3bea17ac3f7235e71a823454ccb74424); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3bea17ac3f7235e71a823454ccb74424)): ?>
<?php $component = $__componentOriginal3bea17ac3f7235e71a823454ccb74424; ?>
<?php unset($__componentOriginal3bea17ac3f7235e71a823454ccb74424); ?>
<?php endif; ?>

        <?php echo view_render_event('sales.reports.index.datagrid.after'); ?>

    </div>
<?php $__env->stopSection(); ?>

<?php if (! $__env->hasRenderedOnce('d7ac9ed3-19b6-45f0-8980-769765b37352')): $__env->markAsRenderedOnce('d7ac9ed3-19b6-45f0-8980-769765b37352');
$__env->startPush('scripts'); ?>
    <script>
        // Add any custom JavaScript for the reports index page
    </script>
<?php $__env->stopPush(); endif; ?>

<?php echo $__env->make('sales::layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm2\laravel-crm\packages\Webkul\Sales\src/resources/views/reports/index.blade.php ENDPATH**/ ?>